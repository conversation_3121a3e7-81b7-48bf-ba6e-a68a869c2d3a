"""
Example sentences to test spaCy and its language models.

>>> from spacy.lang.sl.examples import sentences
>>> docs = nlp.pipe(sentences)
"""


sentences = [
    "Apple načrtuje nakup britanskega startupa za 1 bilijon dolarjev",
    "France Prešeren je umrl 8. februarja 1849 v Kranju",
    "Staro ljubljansko letališče Moste bo obnovila družba BTC",
    "London je največje mesto v Združenem kraljestvu.",
    "Kje se skrivaš?",
    "Kdo je predsednik Francije?",
    "<PERSON><PERSON> je glavno mesto Združenih držav Amerike?",
    "Kdaj je bil rojen Milan Kučan?",
]
