#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Teste de Performance - Comparação entre XTTS-v2 e Kokoro
"""

import time
import sys
from pathlib import Path

# Adicionar src ao path
sys.path.append(str(Path(__file__).parent / 'src'))

from gerador_audiobook import GeradorAudiobook
from configuracao import ConfiguradorAudiobook

def teste_performance_engines():
    """Testa a performance dos dois engines com um texto pequeno"""
    
    # Texto de teste pequeno
    texto_teste = """
    Tópicos de História e Geografia de Goiás. 
    A formação inicial de Goiás foi impulsionada pela mineração de ouro no início do século 18.
    Com o fim do ciclo do ouro, a pecuária tornou-se a principal atividade econômica.
    """
    
    print("🧪 Teste de Performance - XTTS-v2 vs Kokoro")
    print("=" * 50)
    
    # Carregar configurações
    configurador = ConfiguradorAudiobook()
    config = configurador.configuracoes
    
    # Teste 1: XTTS-v2
    print("\n🔧 Testando XTTS-v2...")
    config_xtts = config.copy()
    config_xtts['engine']['primary'] = 'xtts_v2'
    config_xtts['engine']['fallback'] = 'kokoro'
    
    try:
        inicio_xtts = time.time()
        gerador_xtts = GeradorAudiobook(config_xtts)
        
        # Forçar uso do XTTS-v2
        if hasattr(gerador_xtts.engine_primary, 'gerar_audio_segmento'):
            audio_xtts = gerador_xtts.engine_primary.gerar_audio_segmento(texto_teste)
            tempo_xtts = time.time() - inicio_xtts
            
            print(f"✅ XTTS-v2: {tempo_xtts:.2f}s")
            print(f"   📊 Amostras: {len(audio_xtts)}")
            print(f"   🎵 Duração: {len(audio_xtts)/24000:.2f}s")
            print(f"   ⚡ Real-time factor: {tempo_xtts/(len(audio_xtts)/24000):.2f}x")
        else:
            print("❌ XTTS-v2 não disponível")
            tempo_xtts = None
            
    except Exception as e:
        print(f"❌ Erro no XTTS-v2: {e}")
        tempo_xtts = None
    
    # Teste 2: Kokoro
    print("\n🔧 Testando Kokoro...")
    config_kokoro = config.copy()
    config_kokoro['engine']['primary'] = 'kokoro'
    config_kokoro['engine']['fallback'] = 'kokoro'
    
    try:
        inicio_kokoro = time.time()
        gerador_kokoro = GeradorAudiobook(config_kokoro)
        
        audio_kokoro = gerador_kokoro._gerar_audio_kokoro(texto_teste)
        tempo_kokoro = time.time() - inicio_kokoro
        
        print(f"✅ Kokoro: {tempo_kokoro:.2f}s")
        print(f"   📊 Amostras: {len(audio_kokoro)}")
        print(f"   🎵 Duração: {len(audio_kokoro)/24000:.2f}s")
        print(f"   ⚡ Real-time factor: {tempo_kokoro/(len(audio_kokoro)/24000):.2f}x")
        
    except Exception as e:
        print(f"❌ Erro no Kokoro: {e}")
        tempo_kokoro = None
    
    # Comparação
    print("\n📊 Comparação de Performance")
    print("=" * 30)
    
    if tempo_xtts and tempo_kokoro:
        if tempo_kokoro < tempo_xtts:
            speedup = tempo_xtts / tempo_kokoro
            print(f"🏆 Kokoro é {speedup:.1f}x mais rápido que XTTS-v2")
        else:
            speedup = tempo_kokoro / tempo_xtts
            print(f"🏆 XTTS-v2 é {speedup:.1f}x mais rápido que Kokoro")
    
    print("\n💡 Recomendações:")
    if tempo_xtts and tempo_kokoro:
        if tempo_kokoro < tempo_xtts * 0.5:  # Kokoro 2x+ mais rápido
            print("   • Use Kokoro para processamento rápido em lote")
            print("   • Use XTTS-v2 para qualidade máxima (quando tempo não for crítico)")
        else:
            print("   • Ambos os engines têm performance similar")
            print("   • Escolha baseada na qualidade de voz desejada")
    
    print("\n🎯 Configurações Otimizadas Aplicadas:")
    print("   • Batch size aumentado para 4")
    print("   • Otimização de memória ativada")
    print("   • Segmentos menores (150 palavras)")
    print("   • Qualidade média para velocidade")
    print("   • Temperature reduzida (0.65)")
    print("   • Repetition penalty otimizada (3.0)")

if __name__ == "__main__":
    teste_performance_engines()
