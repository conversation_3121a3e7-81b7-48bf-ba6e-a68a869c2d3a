import pytest

from spacy.lang.mk.lex_attrs import like_num


def test_tokenizer_handles_long_text(mk_tokenizer):
    text = """
    Во организациските работи или на нашите собранија со членството, никој од нас не зборуваше за
    организацијата и идеологијата. Работна беше нашата работа, а не идеолошка. Што се однесува до социјализмот на
    Делчев, неговата дејност зборува сама за себе - спротивно. Во суштина, водачите си имаа свои основни погледи и
    свои разбирања за положбата и работите, коишто стоеја пред нив и ги завршуваа со голема упорност, настојчивост и
    насоченост. Значи, идеологија имаше, само што нивната идеологија имаше своја оригиналност. Македонија денеска,
    чиста рожба на животот и положбата во Македонија, кои му служеа како база на неговите побуди, беше дејност која
    имаше потреба од ум за да си најде своја смисла. Таквата идеологија и заемното дејство на умот и срцето му
    помогнаа на Делчев да не се занесе по патот на својата идеологија... Во суштина, Организацијата и нејзините
    водачи имаа свои разбирања за работите и положбата во идеен поглед, но тоа беше врската, животот и положбата во
    Македонија и го внесуваа во својата идеологија гласот на своето срце, и на крај, прибегнуваа до умот,
    за да најдат смисла или да ѝ дадат. Тоа содејство и заемен сооднос на умот и срцето му помогнаа на Делчев да ја
    држи својата идеологија во сообразност со положбата на работите... Водачите навистина направија една жртва
    бидејќи на населението не му зборуваа за своите мисли и идеи. Тие се одрекоа од секаква субјективност во своите
    мисли. Целта беше да не се зголемуваат целите и задачите како и преданоста во работата. Населението не можеше да
    ги разбере овие идеи...
    """
    tokens = mk_tokenizer(text)
    assert len(tokens) == 297


@pytest.mark.parametrize(
    "word,match",
    [
        ("10", True),
        ("1", True),
        ("10.000", True),
        ("1000", True),
        ("бројка", False),
        ("999,0", True),
        ("еден", True),
        ("два", True),
        ("цифра", False),
        ("десет", True),
        ("сто", True),
        ("број", False),
        ("илјада", True),
        ("илјади", True),
        ("милион", True),
        (",", False),
        ("милијарда", True),
        ("билион", True),
    ],
)
def test_mk_lex_attrs_like_number(mk_tokenizer, word, match):
    tokens = mk_tokenizer(word)
    assert len(tokens) == 1
    assert tokens[0].like_num == match


@pytest.mark.parametrize("word", ["двесте", "два-три", "пет-шест"])
def test_mk_lex_attrs_capitals(word):
    assert like_num(word)
    assert like_num(word.upper())


@pytest.mark.parametrize(
    "word",
    [
        "првиот",
        "втора",
        "четврт",
        "четвртата",
        "петти",
        "петто",
        "стоти",
        "шеесетите",
        "седумдесетите",
    ],
)
def test_mk_lex_attrs_like_number_for_ordinal(word):
    assert like_num(word)
