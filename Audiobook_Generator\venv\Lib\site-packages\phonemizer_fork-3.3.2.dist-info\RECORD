../../Scripts/phonemize.exe,sha256=V1ptRaZfqs0WSxGiML8Zsz8ROAqdtfZ-TQXLZuMgm9E,108413
phonemizer/__init__.py,sha256=K2fCZm3vSvD3EMofh-EfbQwHJtwWHBv3vogMIjk_uSU,900
phonemizer/__pycache__/__init__.cpython-312.pyc,,
phonemizer/__pycache__/logger.cpython-312.pyc,,
phonemizer/__pycache__/main.cpython-312.pyc,,
phonemizer/__pycache__/phonemize.cpython-312.pyc,,
phonemizer/__pycache__/punctuation.cpython-312.pyc,,
phonemizer/__pycache__/separator.cpython-312.pyc,,
phonemizer/__pycache__/utils.cpython-312.pyc,,
phonemizer/__pycache__/version.cpython-312.pyc,,
phonemizer/backend/__init__.py,sha256=i4USZR7DxTaAul6BJzK4tcLH7hEEdCXI4J-ZwD3LFeA,1112
phonemizer/backend/__pycache__/__init__.cpython-312.pyc,,
phonemizer/backend/__pycache__/base.cpython-312.pyc,,
phonemizer/backend/__pycache__/segments.cpython-312.pyc,,
phonemizer/backend/base.py,sha256=d6kD6VAIZjFDw03RMcnd2M1kbXbmZ1M9-qFMzVo2vq0,9185
phonemizer/backend/espeak/__init__.py,sha256=yvURen0id8uQ5i3mGhPDbB22euPyjrYppQqnnXXMYGI,735
phonemizer/backend/espeak/__pycache__/__init__.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/api.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/base.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/espeak.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/language_switch.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/mbrola.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/voice.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/words_mismatch.cpython-312.pyc,,
phonemizer/backend/espeak/__pycache__/wrapper.cpython-312.pyc,,
phonemizer/backend/espeak/api.py,sha256=4PuHdemxo10DBoaVS4YPHsc2fULqgpOd8CbmfRRNO_g,9995
phonemizer/backend/espeak/base.py,sha256=EpgtJumAIsn86rU7O7NDN9kqph7T9Imh2yunan-KXYo,3746
phonemizer/backend/espeak/espeak.py,sha256=Gc6Rn5XmC4-o5rZ79LJ9N1RJ44G-UUBrn_18BJcND00,6680
phonemizer/backend/espeak/language_switch.py,sha256=1EdYpWOFwU2slUVg3QROYbcMoMMj7f83hQoBUdICcno,6929
phonemizer/backend/espeak/mbrola.py,sha256=C62MllV9AdgKoQnU09_CrD0BO-Tm6wvnumwk92moNUs,4147
phonemizer/backend/espeak/voice.py,sha256=rRdQiLWoQG4QxYq68L6Vaq2Xa9sft1TuTSZQ1-EskKg,2982
phonemizer/backend/espeak/words_mismatch.py,sha256=qVZlQC-rOUbO635qrc7ctXHHFTOCQayG7h9kUEstGVg,5122
phonemizer/backend/espeak/wrapper.py,sha256=o34nAZOeqdh0TgAzJ8RqW9_du9xHzXduGHoxvVSgAhw,15201
phonemizer/backend/festival/__init__.py,sha256=nHDtSzmHeKbrKEU2mDePVX5LHgBC09qRsLliLpzg8sY,737
phonemizer/backend/festival/__pycache__/__init__.cpython-312.pyc,,
phonemizer/backend/festival/__pycache__/festival.cpython-312.pyc,,
phonemizer/backend/festival/__pycache__/lispy.cpython-312.pyc,,
phonemizer/backend/festival/festival.py,sha256=7Vgv5O6jWw0beZYiadRSnxEiCjqB41UJYoQyLcJKsbs,12303
phonemizer/backend/festival/lispy.py,sha256=xipPoFB-EyFWDznb9AWQcMpYzhGbFJdgZ4EfnzSg9kI,1972
phonemizer/backend/segments.py,sha256=6Sga-tIkMRv5RmhdQTzDMoKyyZzK_6D5OkXv8ZkD9yI,5404
phonemizer/logger.py,sha256=F9IFuMIagEp0wYn3jUF0MOae14bk4l5R0ErFiPbYqjQ,2141
phonemizer/main.py,sha256=O1RUWNXauW4JMVx8jHI4CQIBeExu3sjiO0Mzia13UyA,15824
phonemizer/phonemize.py,sha256=VwJnq_qkxD7H2G8x98Kx2n1Ub1Q_gdfojhXzdh0bStE,13492
phonemizer/punctuation.py,sha256=6Ph8j03KxOJM8iQ-vVFpjqU9TCrCyioGAyTm9vhOw3Q,8764
phonemizer/separator.py,sha256=mbSsXlN8qnxDFyp8waZsnuP8ANuP86a3dj_MkGdF3-E,4151
phonemizer/share/festival/phonemize.scm,sha256=Aj0Oy_kyKF7rQEoVu-H0pPzw4UWNwT1Syu8O6As2VH8,1378
phonemizer/share/segments/chintang.g2p,sha256=4efV_eQiGgRQZL-b53E41YPeCprTJk6NEDcmgJTtyL0,133
phonemizer/share/segments/cree.g2p,sha256=bChrLTuHdbAfR5839Iu6cnqIExVDxyFP1XKGVQyCusw,133
phonemizer/share/segments/inuktitut.g2p,sha256=3OXFjSKUCheayNNdIQsZ_K2hkmL91aPlW3QphUPCz70,93
phonemizer/share/segments/japanese.g2p,sha256=xwlHYCu7pktbEDqrvDEOVyRW3NWeS1GK4W8yt5Wcmg4,192
phonemizer/share/segments/sesotho.g2p,sha256=KCS0sPDGBdyT1QGVRZEwOuCzKn0lbjM_zHXOqf_1CQk,211
phonemizer/share/segments/yucatec.g2p,sha256=I9xvHfwEZlX1JUkuGBzMftH0dQILPtDqCKNXdiwiLrM,297
phonemizer/utils.py,sha256=-FdZCKvdNOiVVuAjhoBzI3tfwpJuzdG4EDuQTWW5CHM,4187
phonemizer/version.py,sha256=BmedCzrHrWrCgiyg1o1ZriHGjNk6w9hDZfxCen9HxPk,2350
phonemizer_fork-3.3.2.dist-info/INSTALLER,sha256=zuuue4knoyJ-UwPPXg8fezS7VCrXJQrAP7zeNuwvFQg,4
phonemizer_fork-3.3.2.dist-info/METADATA,sha256=i4XmH1JuarxM_i-dv1bfxqQ2oWlBFjGG9ob2MrQ-pk0,48256
phonemizer_fork-3.3.2.dist-info/RECORD,,
phonemizer_fork-3.3.2.dist-info/WHEEL,sha256=qtCwoSJWgHk21S1Kb4ihdzI2rlJ1ZKaIurTj_ngOhyQ,87
phonemizer_fork-3.3.2.dist-info/entry_points.txt,sha256=PgnnDOPvchzkWgnlrKRj7yTCWa8J8DaBRbn4dizP5ZA,51
phonemizer_fork-3.3.2.dist-info/licenses/LICENSE,sha256=1i8GWDCqNznMAxFWuWkIBcey6BG0oXjItKzYcl1WHJQ,35142
