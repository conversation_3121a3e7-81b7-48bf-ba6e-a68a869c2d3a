from ...symbols import NORM, ORTH
from ...util import update_exc
from ..tokenizer_exceptions import BASE_EXCEPTIONS
from ._tokenizer_exceptions_list import MS_BASE_EXCEPTIONS

# Daftar singkatan dan <PERSON>im dari:
# https://ms.wiktionary.org/wiki/Wiktionary:Senarai_akronim_dan_singkatan

_exc = {}

for orth in MS_BASE_EXCEPTIONS:
    _exc[orth] = [{ORTH: orth}]
    orth_title = orth.title()
    _exc[orth_title] = [{ORTH: orth_title}]
    orth_caps = orth.upper()
    _exc[orth_caps] = [{ORTH: orth_caps}]
    orth_lower = orth.lower()
    _exc[orth_lower] = [{ORTH: orth_lower}]
    orth_first_upper = orth[0].upper() + orth[1:]
    _exc[orth_first_upper] = [{ORTH: orth_first_upper}]
    if "-" in orth:
        orth_title = "-".join([part.title() for part in orth.split("-")])
        _exc[orth_title] = [{ORTH: orth_title}]
        orth_caps = "-".join([part.upper() for part in orth.split("-")])
        _exc[orth_caps] = [{ORTH: orth_caps}]

for exc_data in [
    {ORTH: "Jan.", NORM: "Januari"},
    {ORTH: "Feb.", NORM: "Februari"},
    {ORTH: "Mac.", NORM: "Mac"},
    {ORTH: "Apr.", NORM: "April"},
    {ORTH: "Jun.", NORM: "Jun"},
    {ORTH: "Jul.", NORM: "Julai"},
    {ORTH: "Ogos.", NORM: "Ogos"},
    {ORTH: "Sep.", NORM: "September"},
    {ORTH: "Okt.", NORM: "Oktober"},
    {ORTH: "Nov.", NORM: "November"},
    {ORTH: "Dis.", NORM: "Disember"},
]:
    _exc[exc_data[ORTH]] = [exc_data]

_other_exc = {
    "do'a": [{ORTH: "do'a", NORM: "doa"}],
    "jum'at": [{ORTH: "jum'at", NORM: "Jumat"}],
    "Jum'at": [{ORTH: "Jum'at", NORM: "Jumat"}],
    "la'nat": [{ORTH: "la'nat", NORM: "laknat"}],
    "ma'af": [{ORTH: "ma'af", NORM: "maaf"}],
    "mu'jizat": [{ORTH: "mu'jizat", NORM: "mukjizat"}],
    "Mu'jizat": [{ORTH: "Mu'jizat", NORM: "mukjizat"}],
    "ni'mat": [{ORTH: "ni'mat", NORM: "nikmat"}],
    "raka'at": [{ORTH: "raka'at", NORM: "rakaat"}],
    "ta'at": [{ORTH: "ta'at", NORM: "taat"}],
}

_exc.update(_other_exc)

for orth in [
    "1 Kor.",
    "1 Ptr.",
    "1 Raj.",
    "1 Sam.",
    "1 Taw.",
    "1 Tes.",
    "1 Tim.",
    "1 Yoh.",
    "1Ch.",
    "1Co.",
    "1Jo.",
    "1Ki.",
    "1Pe.",
    "1Sa.",
    "1Th.",
    "1Ti.",
    "2 Kor.",
    "2 Ptr.",
    "2 Raj.",
    "2 Sam.",
    "2 Taw.",
    "2 Tes.",
    "2 Tim.",
    "2 Yoh.",
    "2Ch.",
    "2Co.",
    "2Jo.",
    "2Ki.",
    "2Pe.",
    "2Sa.",
    "2Th.",
    "2Ti.",
    "3 Yoh.",
    "3D",
    "3F",
    "3Jo.",
    "3M",
    "8MP",
    "AA",
    "AAAAAA",
    "AB",
    "Abd.",
    "ABC",
    "ABIM",
    "ABM",
    "ABMI",
    "ABS",
    "AC",
    "Ac",
    "ACAPLPL",
    "Act.",
    "AD",
    "AD LIB",
    "ADAM",
    "ADB",
    "ADD",
    "ADIL",
    "ADN",
    "ADR",
    "ADRI",
    "ADSL",
    "ADUN",
    "AFAS",
    "AFTA",
    "Ag",
    "AGMARIS",
    "AH",
    "AI",
    "AIA",
    "AIDS",
    "AIJV",
    "AIM",
    "a/k",
    "ak",
    "AKN",
    "Al",
    "a/l",
    "AM",
    "Am",
    "Am.",
    "AMN",
    "Amo.",
    "AMPS",
    "Ams.",
    "AMWA",
    "AN",
    "a.n.",
    "ANGKASA",
    "ANM",
    "ANSI",
    "Ant.",
    "AOL",
    "AP",
    "a/p",
    "APD",
    "APEC",
    "API",
    "APIK",
    "APM",
    "APN",
    "APP",
    "Apr.",
    "APRI",
    "Ar",
    "Ar.",
    "ark.",
    "A.S.",
    "As",
    "a.s.",
    "ASA",
    "ASAS 50",
    "ASB",
    "ASCII",
    "ASEAN",
    "ASEAN+3",
    "ASEM",
    "a.s.f.",
    "ASN",
    "a.s.o.",
    "ASP",
    "Ast.",
    "A.T.",
    "At",
    "ATM",
    "a.t.r.",
    "ATUR",
    "Au",
    "AURI",
    "Aug.",
    "AWOL",
    "Ayb.",
    "B",
    "BA",
    "Ba",
    "BAC",
    "BAFIA",
    "BAM",
    "BANANA",
    "BAPP",
    "BASF",
    "BATA",
    "BB",
    "BBC",
    "BBE",
    "BBS",
    "BC",
    "BCG",
    "BCIC",
    "b.d.",
    "BDSSHAM",
    "Be",
    "BEER",
    "BERNAMA",
    "Bh",
    "b.h.",
    "Bhd.",
    "Bi",
    "BIDS",
    "Bil.",
    "bil.",
    "BIMP-EAGA",
    "Bio.",
    "BIOS",
    "BITMB",
    "BJ",
    "Bk",
    "b.k.",
    "BKAL",
    "bkn.",
    "BKP",
    "BL",
    "BLR",
    "BM",
    "BMI",
    "BMW",
    "BN",
    "BNM",
    "BO",
    "BOJ",
    "BOO",
    "BOP",
    "BOT",
    "BP",
    "b.p.",
    "BPA",
    "BPAs",
    "bpd.",
    "BPIMB",
    "BPM",
    "BPO",
    "BPPH",
    "Br",
    "Br.",
    "BSA",
    "B.Sc.",
    "B.Sh.",
    "b.s.j.",
    "BSN",
    "Bt.",
    "bt.",
    "BWT",
    "BYOB",
    "C",
    "C.",
    "C/E",
    "Ca",
    "CAAM",
    "CAD",
    "CAM",
    "CATV",
    "CBS",
    "CBT",
    "CC",
    "CCD",
    "CCM",
    "CCR",
    "cct-km",
    "CCTV",
    "CCU",
    "CD",
    "Cd",
    "CD-ROM",
    "CD-RW",
    "CDRC",
    "Ce",
    "CEO",
    "CEPT",
    "Cetak",
    "Cf",
    "CFO",
    "CFTC",
    "CGC",
    "CGI",
    "CH",
    "CIA",
    "CIAST",
    "CID",
    "CIDB",
    "CIQ",
    "CKD",
    "CL",
    "Cl",
    "c.l.",
    "CLI",
    "CLOB",
    "CM",
    "Cm",
    "cm.",
    "CMAG",
    "CMI",
    "CMP",
    "CNN",
    "Co",
    "COD",
    "Col.",
    "COLA",
    "COMDEX",
    "CP",
    "CPI",
    "CPO",
    "CPR",
    "CPU",
    "Cr",
    "CRDF",
    "Cs",
    "CST",
    "CT",
    "CTIP",
    "CTRM",
    "Cu",
    "CUEPACS",
    "D-8",
    "d/a",
    "DAGS",
    "Dan.",
    "DANCED",
    "DAP",
    "DARA",
    "Db",
    "DBKL",
    "DBP",
    "DBR",
    "DC",
    "DDA",
    "DDT",
    "DEB",
    "Dec.",
    "Deu.",
    "DFIs",
    "dgn.",
    "DHL",
    "DIBML",
    "DIN",
    "Dis.",
    "DJ",
    "d.l.l.",
    "dlm.",
    "dng.",
    "DNS",
    "DO",
    "DOA",
    "DOE",
    "DOF",
    "DOSH",
    "doz.",
    "DPPS",
    "Dr.",
    "dr.",
    "drp.",
    "drpd.",
    "Ds",
    "d.sb.",
    "d.st.",
    "DSTN2",
    "Dt.",
    "DTAs",
    "DTMF",
    "DTP",
    "DTV",
    "DUBES",
    "DUNHILL",
    "DV8",
    "DVD",
    "DVE",
    "DVS",
    "dw.t.",
    "Dy",
    "DYMM",
    "E",
    "E-Commerce",
    "E-Dagang",
    "E&E",
    "E-Faraid",
    "E-Government",
    "E-Kerajaan",
    "E-Mail",
    "E-Services",
    "E-Village",
    "E-Zine",
    "EALAF",
    "EBI",
    "EBP",
    "EC",
    "ECAFE",
    "Ecc.",
    "ECI",
    "ECM",
    "ECOSOC",
    "ECP",
    "ECR",
    "EDI",
    "EE",
    "EEC",
    "Ef.",
    "EG",
    "Eko.",
    "EKS",
    "ELWS",
    "ELX",
    "EMI",
    "EMUs",
    "En.",
    "EP",
    "EPF",
    "Eph.",
    "EPP",
    "EPS",
    "EPU",
    "ER",
    "Er",
    "ERL",
    "ERT",
    "Es",
    "ESCAP",
    "ESOS",
    "ESP",
    "EST",
    "Est.",
    "ET",
    "ETA",
    "ETACS",
    "ETC",
    "ETD",
    "EU",
    "Eu",
    "EVIAN",
    "Exim Bank",
    "Exo.",
    "Eze.",
    "Ezr.",
    "F",
    "FAM",
    "FAMA",
    "FAO",
    "FAQ",
    "FAX",
    "FBI",
    "FC",
    "FCA",
    "FCC",
    "FDI",
    "FE",
    "Fe",
    "f.e.",
    "Feb.",
    "FELCRA",
    "FELDA",
    "FI",
    "FIA 1993",
    "FIAT",
    "FIC",
    "FIDA",
    "FIFA",
    "FIMA",
    "Fiz.",
    "Flm.",
    "Flp.",
    "FM",
    "Fm",
    "FMUTM",
    "FO",
    "FOA",
    "FOB",
    "FOC",
    "FOMCA",
    "FORD",
    "Fr",
    "FRIM",
    "FRTI",
    "FSMP",
    "FTA",
    "FTE",
    "FTP",
    "G",
    "g.",
    "G15",
    "G77",
    "Ga",
    "GAC",
    "GACM",
    "Gal.",
    "GAPENA",
    "GATS",
    "GATT",
    "GB",
    "Gbps.",
    "Gd",
    "GDP",
    "Ge",
    "GEC",
    "Gen.",
    "Geo.",
    "Geog.",
    "Gerakan",
    "GH",
    "GIF",
    "GII",
    "GIS",
    "GITIC",
    "GITN",
    "GJ",
    "GLCs",
    "GM",
    "GMBH",
    "GMI",
    "GMT",
    "GNP",
    "GNS",
    "GOLD",
    "GP",
    "GPC",
    "GPIM",
    "GPMS",
    "GPO",
    "GPP",
    "GPS",
    "GRO",
    "GRS",
    "GSMC",
    "GST",
    "GTZ",
    "GUI",
    "GWh.",
    "H",
    "Ha",
    "Hab.",
    "Hag.",
    "Hak.",
    "ham",
    "hb.",
    "HCI",
    "HDTV",
    "He",
    "Heb.",
    "Hf",
    "Hg",
    "HI-FI",
    "HIS",
    "HIV",
    "Hj.",
    "HMS",
    "Ho",
    "Hos.",
    "HP",
    "HRDC",
    "HRDF",
    "HRMIS",
    "Hs",
    "Hut.",
    "I",
    "I/O",
    "IA",
    "IAA",
    "IADPs",
    "IB",
    "i.b.",
    "IBA",
    "IBFIM",
    "IBG",
    "Ibr.",
    "IBRD",
    "IBS",
    "IC",
    "ICA",
    "ICBM",
    "ICFM",
    "ICI",
    "ICM",
    "ICOR",
    "ICP",
    "ICT",
    "ICU",
    "ID",
    "Id.",
    "IDB",
    "IDFR",
    "IE",
    "i.e.",
    "IFSB",
    "IGAs",
    "IGS",
    "IHP",
    "IHPG",
    "IIM",
    "IINA",
    "IKKL",
    "IKP",
    "IKPH",
    "IKS",
    "Im.",
    "IMD",
    "IMF",
    "IMP2",
    "IMR",
    "IMS-GT",
    "IMT-GT",
    "In",
    "in.",
    "INFRA",
    "INSEP",
    "INSPEN",
    "INTAN",
    "IOFC",
    "IOU",
    "IP",
    "IPA",
    "IPBA",
    "IPCs",
    "IPEBP",
    "IPI",
    "IPKIM",
    "IPKPM",
    "IPO",
    "IPP",
    "IPPM",
    "IPPPM",
    "i.pt.",
    "IPTAR",
    "IPTNM",
    "IQR",
    "Ir",
    "IRA",
    "IRPA",
    "IRS",
    "i.s.",
    "ISA",
    "Isa.",
    "ISDN",
    "ISMM",
    "ISO",
    "ISP",
    "ist.",
    "IT",
    "i.t.",
    "ITA",
    "ITAF",
    "ITEX",
    "ITK",
    "ITM",
    "ITO",
    "ITRCo",
    "ITTA",
    "ITU",
    "JAK",
    "JAKIM",
    "Jam.",
    "Jan.",
    "Jb.",
    "JBIC",
    "JD",
    "JDA",
    "Jdg.",
    "Jer.",
    "Jh.",
    "JICA",
    "JJ",
    "Jk.",
    "JKKK",
    "jkps.",
    "JKR",
    "JMTI",
    "JOA",
    "Joe.",
    "Joh.",
    "Jon.",
    "Jos.",
    "JP",
    "JPA",
    "JPEG",
    "JPH",
    "JPJ",
    "JPSHK",
    "JPS",
    "JPT",
    "JRDA",
    "JSM",
    "JT",
    "Jud.",
    "Jul.",
    "Jun.",
    "JVC",
    "Jw.",
    "K",
    "K-Economy",
    "KADA",
    "KBE",
    "KBIA",
    "KBPA",
    "KBSM",
    "KD",
    "Kd.",
    "KDI",
    "KDN",
    "KDNK",
    "KE",
    "KEAP",
    "Kej.",
    "Kel.",
    "KEM",
    "KEMLU",
    "kep.",
    "Kg.",
    "kg.",
    "KGB",
    "KGK",
    "KH",
    "ki.",
    "Kid.",
    "KIK",
    "KIKMTT",
    "KIM",
    "Kim.",
    "Kis.",
    "KIX",
    "KKGSK",
    "KKK",
    "KKPPA",
    "KL",
    "Kl.",
    "KLCI",
    "KLIA",
    "KLIBOR",
    "KLIM",
    "KLM",
    "KLSE",
    "KM",
    "KMM",
    "KNK",
    "KO",
    "Kol.",
    "Kom.",
    "Komp.",
    "KOMSAS",
    "KPAI",
    "KPB",
    "KPBA",
    "KPC",
    "kpd.",
    "KPE",
    "KPIs",
    "KPPL",
    "KPPMS",
    "KPWM",
    "Kr",
    "KRM",
    "KSTI",
    "KT",
    "KTA",
    "KTABKL",
    "KTM",
    "KTMB",
    "kV",
    "kW",
    "kWh",
    "kWj",
    "KWSP",
    "LA",
    "La",
    "LABOR",
    "Lam.",
    "LAN",
    "LAPD",
    "LASER",
    "LAX",
    "lb.",
    "LC",
    "LCD",
    "LCHRF",
    "LCLY",
    "LED",
    "Lev.",
    "LFPR",
    "LFS",
    "LFX",
    "LGM",
    "Li",
    "LID",
    "Lin.",
    "LKN",
    "LKPM",
    "LKPP",
    "LKTP",
    "LKWJ",
    "LLB",
    "LLC",
    "LLN",
    "LLS",
    "LMSM",
    "LNG",
    "LOA",
    "LOBATA",
    "LOFSA",
    "LPG",
    "LPIP",
    "LPKI",
    "LPKLPL",
    "LPKN",
    "LPN",
    "LPP",
    "LPPK",
    "LPPM",
    "LPPP",
    "LPPTP",
    "Lr",
    "LRs",
    "LRT",
    "LS",
    "LTAKL",
    "LTD",
    "LTK",
    "Lu",
    "LUAS",
    "Luk.",
    "lw.",
    "lwn.",
    "M\n",
    "m",
    "M&A",
    "MAB",
    "MACRES",
    "MAD",
    "MADA",
    "MAGERAN",
    "MAHA",
    "MAHSURI",
    "Mal.",
    "MALINDO",
    "MAMPU",
    "Mar.",
    "MARA",
    "MARC",
    "MARDI",
    "MARLBORO",
    "MAS",
    "MASSA",
    "MASSCORP",
    "Mat.",
    "MATRADE",
    "MAVCAP",
    "MB",
    "MBA",
    "MBBS",
    "MBM",
    "MBO",
    "MBS",
    "MBTU",
    "MC",
    "MCA",
    "MCB",
    "MCSL",
    "MCSv5",
    "MD",
    "Md",
    "MDB",
    "MDC",
    "MDG",
    "MDV",
    "MEASAT",
    "MEATJ",
    "MECIB",
    "MEMO",
    "MENLU",
    "MEPS",
    "MES",
    "MESDAQ",
    "METEOR",
    "MFI",
    "MFIs",
    "MG",
    "Mg",
    "MGM",
    "MGR",
    "MGS",
    "MHA",
    "Mi.",
    "MIA",
    "MIB",
    "MIC",
    "Mic.",
    "MICE",
    "MIDA",
    "MIDF",
    "MIDI",
    "MIG",
    "MIGHT",
    "MII",
    "MIMOS",
    "MINDEF",
    "MINT",
    "mis.",
    "MIT",
    "MITC",
    "MITI",
    "Ml.",
    "MLNG",
    "mlpd.",
    "MM",
    "mm",
    "MMN",
    "mmscfd.",
    "MMU",
    "MMX",
    "Mn",
    "Mn.",
    "MNA",
    "MNCs",
    "MO",
    "Mo",
    "MOA",
    "MOD",
    "MODEM",
    "MOE",
    "MOH",
    "MOSTE",
    "MOSTI",
    "MOU",
    "MP",
    "MPB",
    "MPEG",
    "MPOB",
    "MPP",
    "mppa.",
    "MPPJ",
    "MPS",
    "MPTM",
    "MR",
    "m.r.",
    "MRB",
    "MRELB",
    "Mrk.",
    "MRRDB",
    "MS",
    "MS-DOS",
    "MSC",
    "MSG",
    "MSM",
    "Mt",
    "MTC",
    "MTCP",
    "MTD",
    "MTDC",
    "MTPB",
    "MTV",
    "Muz.",
    "MV",
    "MW",
    "MY",
    "MyKe",
    "Mzm.",
    "N",
    "N/A",
    "Na",
    "NAB",
    "NACIWID",
    "Nah.",
    "NAP",
    "NASA",
    "NATO",
    "NAV",
    "NB",
    "Nb",
    "NBA",
    "NBC",
    "NCR",
    "Nd",
    "NDP",
    "Ne",
    "NEAC",
    "NEC",
    "NEF",
    "Neh.",
    "NEP",
    "NEqO",
    "NERP",
    "NF",
    "NFPEs",
    "NG",
    "NGOs",
    "NGV",
    "NHEF",
    "NHHES",
    "NHK",
    "Ni",
    "NIDC",
    "NIH",
    "NIP",
    "NIPA",
    "NIS",
    "NISIR",
    "NITA",
    "NITC",
    "NITP",
    "NIV",
    "NLAC",
    "NMPBSP",
    "NMU",
    "No",
    "No.",
    "no.",
    "NOSS",
    "Nov.",
    "Np",
    "NPC",
    "NPCS",
    "NPL",
    "NRCC",
    "NRW",
    "NS",
    "Ns",
    "NSB",
    "NTA",
    "NTHRDC",
    "NTMP",
    "NTSC",
    "Num.",
    "NUTF",
    "NVP",
    "NVTC",
    "NWRC",
    "O",
    "Ob.",
    "Oba.",
    "OC",
    "OCPD",
    "Oct.",
    "OD",
    "ODA",
    "OECD",
    "OEM",
    "Ogo.",
    "OHQs",
    "OIC",
    "Okt.",
    "OPEC",
    "OPP",
    "OPP3",
    "OPR",
    "OS",
    "Os",
    "OSA",
    "OT",
    "OUG",
    "oz.",
    "P",
    "P&P",
    "PA",
    "Pa",
    "PABK",
    "PABX",
    "PAK",
    "PAKSI",
    "PAL",
    "PALL MALL",
    "PAS",
    "PATA",
    "PAWS",
    "Pb",
    "PBA",
    "PBB",
    "PBM",
    "PBP",
    "PBSM",
    "PBT",
    "PC",
    "PC(s)",
    "PCB",
    "PCIRITA",
    "PCM",
    "PCMCIA",
    "PCN",
    "PD",
    "Pd",
    "pd.",
    "PDS",
    "PE",
    "PEKEMAS",
    "PEMADAM",
    "PENA",
    "PENIS",
    "PERDANA",
    "PERKESO",
    "PERKIM",
    "PERNAS",
    "PERTAMA",
    "PERTIWI",
    "PESAKA",
    "PETA",
    "PETRONAS",
    "PGU",
    "Ph.",
    "PHD",
    "Phi.",
    "Phm.",
    "PIK",
    "PIKOM",
    "PIN",
    "PINTAS",
    "PIPM",
    "PISK",
    "PITA",
    "PIXEL",
    "PJ",
    "PJK",
    "PJKB",
    "PJP",
    "PKBM",
    "PKBTA",
    "PKEN",
    "Pkh.",
    "PKKM",
    "PKLPA",
    "PKM",
    "PKNS",
    "PKPIM",
    "PKPM",
    "PKR",
    "PKS",
    "Pl.",
    "p.l.",
    "PLA",
    "PLC",
    "PLCHP",
    "PLCs",
    "PLI",
    "PLT",
    "PLUS",
    "PLWS",
    "PM",
    "Pm",
    "PMM",
    "PMP",
    "PMR",
    "PMS",
    "Pn.",
    "PNAT",
    "PNS",
    "PO",
    "Po",
    "POCPA",
    "POKEMON",
    "Pol.",
    "POP",
    "PORIM",
    "PORLA",
    "PORTAFOAM",
    "PP",
    "PPA",
    "PPBE",
    "PPBK",
    "ppd.",
    "PPGM",
    "PPI",
    "PPK",
    "PPL",
    "PPM",
    "PPP",
    "PPPB",
    "PPPLM",
    "PPPM",
    "PPR",
    "PPRT",
    "PPS",
    "PPTM",
    "PPU",
    "PR",
    "Pr",
    "Pr.",
    "prb.",
    "PRI",
    "PRO",
    "Pro.",
    "Prof.",
    "PROSPER",
    "PROSTAR",
    "PROTON",
    "PS",
    "PSA",
    "Psa.",
    "PSCs",
    "PSDC",
    "PSDH",
    "Psi.",
    "PSKE",
    "PSRM",
    "PST",
    "PT",
    "Pt",
    "PTD",
    "PTP",
    "Pu",
    "PUNB",
    "QA",
    "QC",
    "QCC",
    "R&D",
    "RA",
    "Ra",
    "RAM",
    "RAPP",
    "Rat.",
    "Rb",
    "RCA",
    "RDA",
    "RDAs",
    "RDCs",
    "RE",
    "Re",
    "REHDA",
    "Rev.",
    "Rf",
    "Rg",
    "RGB",
    "Rh",
    "RI",
    "RIDA",
    "RIP",
    "RISDA",
    "r.l.",
    "RM",
    "Rm.",
    "RMKe-8",
    "Rn",
    "ROC",
    "ROM",
    "Rom.",
    "RPG",
    "RPS",
    "RRI",
    "RRIM",
    "RRJP",
    "RRP",
    "RSGC",
    "RSS",
    "RSVP",
    "Rt.",
    "RTA",
    "RTM",
    "Ru",
    "Rut.",
    "RWCR",
    "RX",
    "S",
    "S/N",
    "S&T",
    "S-VHS",
    "SA",
    "SAC",
    "SADCs",
    "SAGA",
    "SALCRA",
    "SALM",
    "SALT",
    "SAM",
    "SAP",
    "SARS",
    "Sas.",
    "s.a.w.",
    "SB",
    "Sb",
    "Sb.",
    "SBA",
    "SBB",
    "sbg.",
    "SBK",
    "SC",
    "Sc",
    "SCA",
    "SCADA",
    "SCANS",
    "SCSI",
    "SCuM",
    "SDCs",
    "Sdn. Bhd.",
    "sdr.",
    "SDRC",
    "Se",
    "SEATO",
    "SEB",
    "SECAM",
    "SEDCs",
    "SEFF",
    "Sej.",
    "SEMS",
    "Sep.",
    "Sept.",
    "SESB",
    "SESCo",
    "s.f.",
    "Sg",
    "SGPCA",
    "SGPPI",
    "SGPPKRM",
    "SGX",
    "Si",
    "Si.",
    "SIA 1983",
    "SIC",
    "SIM",
    "SING",
    "SIRIM",
    "SITTDEC",
    "sj.",
    "SKDTP",
    "SKM",
    "SKSM",
    "SL",
    "Sl.",
    "sl.",
    "SLMCH",
    "SLR",
    "SM",
    "Sm",
    "SMART",
    "SMEs",
    "SMEt",
    "SMIs",
    "SMIDEC",
    "SMIDP",
    "SMJK",
    "SMR",
    "SMS",
    "SMT",
    "SMTP",
    "SN",
    "Sn",
    "SOB",
    "SOCSO",
    "SOHO",
    "Son.",
    "SOS",
    "Sos.",
    "SP",
    "SPA",
    "SPAM",
    "SPCA",
    "SPKR",
    "SPLAM",
    "SPM",
    "SPNB",
    "SPSP",
    "t.",
    "Ta",
    "Tadb.",
    "TAF",
    "TAF-W",
    "Tani",
    "TAP",
    "TAR",
    "TARBI",
    "TB",
    "Tb",
    "TBA",
    "TBTP",
    "Tc",
    "TCPD",
    "TDCs",
    "Te",
    "TEKUN",
    "TELCO",
    "TELEX",
    "TEUs",
    "TFP",
    "TGV",
    "TH",
    "Th",
    "THIS",
    "Ti",
    "TICAD",
    "Tit.",
    "TKA",
    "Tks.",
    "Tl",
    "TLDM",
    "TM",
    "Tm",
    "TMB",
    "TMK",
    "TNB",
    "TNSB",
    "TNT",
    "TOEFL",
    "TP",
    "TPIM",
    "TPK",
    "TPPP",
    "TPPT",
    "TPSM",
    "TPUB",
    "TQM",
    "Tr.",
    "TRIPs",
    "tsb.",
    "tscf.",
    "t.sh.",
    "t.s.t.",
    "TT",
    "t.t.",
    "TUDM",
    "TV",
    "TVSMR",
    "TWAIN",
    "TX",
    "TYPHIrapid",
    "U",
    "Ubat",
    "UDA",
    "Udg.",
    "UFO",
    "UH",
    "UIA",
    "UiTM",
    "UK",
    "UKM",
    "UL",
    "Ul.",
    "ULC",
    "UM",
    "UMNO",
    "UMS",
    "UN",
    "UN/OSCAL",
    "UNCLE",
    "UNCTAD",
    "UNDP",
    "UNESCO",
    "UNFCCC",
    "UNFPA",
    "UNHCR",
    "UNICEF",
    "UNIMAS",
    "UNTAET",
    "UPE",
    "UPM",
    "UPS",
    "UPSR",
    "URL",
    "US",
    "USAINS",
    "USD",
    "USM",
    "USNO",
    "USS",
    "USSR",
    "UTC",
    "UTF",
    "utk.",
    "UTM",
    "V",
    "VAT",
    "VCC",
    "VCD",
    "VCR",
    "VD",
    "VDSC",
    "VGA",
    "VHF",
    "VHS",
    "VIP",
    "VMS",
    "VO",
    "VOA",
    "VoIP",
    "VR",
    "VSOP",
    "VW",
    "W",
    "W/O",
    "WAP",
    "WAY",
    "WC",
    "WDDM",
    "WDM",
    "WHO",
    "Why.",
    "WIM",
    "WPG",
    "WTO",
    "WWF",
    "WWW",
    "WYSIWYG",
    "Xe",
    "XO",
    "XXL",
    "Y",
    "Y2K",
    "YAB",
    "Yak.",
    "YAM",
    "YAS",
    "YB",
    "Yb",
    "Yeh.",
    "Yer.",
    "Yes.",
    "yg.",
    "Yl.",
    "YM",
    "YMCA",
    "Yoh.",
    "Yos.",
    "Y.Th.",
    "YTM",
    "Yud.",
    "Yun.",
    "Za.",
    "Zec.",
    "Zef.",
    "Zep.",
    "ZIP",
    "Zn",
    "Zr",
]:
    _exc[orth] = [{ORTH: orth}]

TOKENIZER_EXCEPTIONS = update_exc(BASE_EXCEPTIONS, _exc)
