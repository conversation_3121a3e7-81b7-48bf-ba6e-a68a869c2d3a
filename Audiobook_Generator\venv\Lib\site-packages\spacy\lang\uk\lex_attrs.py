from ...attrs import LIKE_NUM

_num_words = [
    "більйон",
    "вісім",
    "вісімдесят",
    "вісімнадцять",
    "вісімсот",
    "восьмий",
    "два",
    "двадцять",
    "дванадцять",
    "двісті",
    "дев'яносто",
    "дев'ятнадцять",
    "дев'ятсот",
    "дев'ять",
    "десять",
    "децильйон",
    "квадрильйон",
    "квінтильйон",
    "мільйон",
    "мільярд",
    "нонильйон",
    "один",
    "одинадцять",
    "октильйон",
    "п'ятий",
    "п'ятисотий",
    "п'ятнадцять",
    "п'ятсот",
    "п'ять",
    "секстильйон",
    "септильйон",
    "сім",
    "сімдесят",
    "сімнадцять",
    "сімсот",
    "сорок",
    "сто",
    "тисяча",
    "три",
    "тридцять",
    "трильйон",
    "тринадцять",
    "триста",
    "чотири",
    "чотириста",
    "чотирнадцять",
    "шістдесят",
    "шістнадцять",
    "шістсот",
    "шість",
]


def like_num(text):
    text = text.replace(",", "").replace(".", "")
    if text.isdigit():
        return True
    if text.count("/") == 1:
        num, denom = text.split("/")
        if num.isdigit() and denom.isdigit():
            return True
    if text in _num_words:
        return True
    return False


LEX_ATTRS = {LIKE_NUM: like_num}
